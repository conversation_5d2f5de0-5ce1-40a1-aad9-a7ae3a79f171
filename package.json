{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "0BSD", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "1.23.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.1.1", "expo": "~53.0.0", "expo-linear-gradient": "^14.0.2", "expo-localization": "~16.0.1", "expo-status-bar": "~2.0.1", "firebase": "^11.6.0", "i18n-js": "^4.5.1", "lottie-ios": "^4.5.1", "lottie-react-native": "7.1.0", "react": "18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-localize": "^3.4.1", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-dom": "18.3.1", "react-native-web": "~0.19.13", "@expo/metro-runtime": "~4.0.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@react-native-community/cli": "latest"}, "private": true}